import { useEffect, useState } from 'react';
import { useGraphQLQuery } from '../../../hooks/useGraphQLQuery';
import { MicrosoftLoginUrlRes } from '../../../types/User';
import { OPERATION_NAME, QUERY_KEY } from '../../../constants/common';
import { AUTH_GET_MICROSOFT_LOGIN_URL } from '../../../services/UserService';
import { useNavigate } from 'react-router-dom';
import './MicrosoftDirectLoginForm.css';

export default function MicrosoftDirectLoginForm() {
    const [email, setEmail] = useState('');
    const [isLoading, setIsLoading] = useState(false);
    const [shouldFetch, setShouldFetch] = useState(false);
    const navigate = useNavigate();

    const {
        data,
        isLoading: isFetching,
        isError,
    } = useGraphQLQuery<MicrosoftLoginUrlRes>(
        [QUERY_KEY.MICROSOFT_LOGIN_URL, email],
        AUTH_GET_MICROSOFT_LOGIN_URL,
        {
            email,
        },
        OPERATION_NAME.LOGIN,
        {
            enabled: shouldFetch,
        }
    );

    useEffect(() => {
        if (data && data.auth_get_azure_login_url.url) {
            window.location.href = data.auth_get_azure_login_url.url;
        }
    }, [data]);

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();

        if (!email.trim()) {
            return;
        }

        setIsLoading(true);
        setShouldFetch(true);
    };

    const handleEmailChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        setEmail(e.target.value);
    };

    return (
        <div className="modern-login-container">
            <div className="login-card">
                {/* Logo Section */}
                <div className="logo-section">
                    <img src="/assets/images/logo/new-lspbanner.png" alt="LSP Logo" className="company-logo" />
                </div>

                {/* Title Section */}
                <div className="title-section">
                    <h1 className="login-title">e-MOC System</h1>
                </div>

                {/* Form Section */}
                <form onSubmit={handleSubmit} className="login-form">
                    <div className="floating-input-group">
                        <input
                            type="email"
                            id="microsoft-email"
                            className="floating-input"
                            value={email}
                            onChange={handleEmailChange}
                            required
                            disabled={isLoading}
                            placeholder=" "
                        />
                        <label htmlFor="microsoft-email" className="floating-label">
                            Email
                        </label>
                    </div>

                    <div className="button-group-center">
                        <button type="submit" className="btn-primary-center" disabled={isLoading || !email.trim()}>
                            {isLoading ? 'Processing...' : 'Login'}
                        </button>
                    </div>
                </form>

                {/* Company Info */}
                <div className="company-info">LONG SON PETROCHEMICALS CO.,LTD</div>
            </div>
        </div>
    );
}
