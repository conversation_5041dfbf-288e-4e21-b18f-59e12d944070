import { gql } from 'graphql-request';

export const WORKFLOW_LIST = gql`
    query Workflow_definition_list_paginate(
        $page: Int!
        $limit: Int!
        $filters: [String!]
        $sort: String
        $search: String
    ) {
        workflow_definition_list_paginate(
            input: { search: $search, filters: $filters, sort: $sort, page: $page, limit: $limit }
        ) {
            totalCount
            totalPages
            currentPage
            data {
                id
                created_by
                updated_by
                created_at
                updated_at
                deleted_at
                name
                bpmnXml
                status
                camunda_key
                camunda_id
                version
                workflow_steps {
                    id
                    step_key
                    step_name
                    workflow_definition {
                        id
                        created_by
                        updated_by
                        created_at
                        updated_at
                        deleted_at
                        name
                        bpmnXml
                        status
                        camunda_key
                        camunda_id
                        version
                    }
                }
                workflow_instances {
                    id
                    created_by
                    updated_by
                    created_at
                    updated_at
                    deleted_at

                    process_instance_id
                    camunda_key
                    business_key
                    name
                    status
                    started_at
                    ended_at
                    form_data
                    camunda_variables
                }
            }
        }
    }
`;

export const GET_WORKFLOW = gql`
    query Workflow_definition_detail($id: String!) {
        workflow_definition_detail(id: $id) {
            id
            created_by
            updated_by
            created_at
            updated_at
            deleted_at
            name
            bpmnXml
            status
            camunda_key
            camunda_id
            version
            workflow_steps {
                id
                step_key
                step_name
            }
            workflow_instances {
                id
                created_by
                updated_by
                created_at
                updated_at
                deleted_at
                process_instance_id
                camunda_key
                business_key
                name
                status
                started_at
                ended_at
                form_data
                camunda_variables
            }
            creator {
                id
                created_by
                updated_by
                created_at
                updated_at
                deleted_at
                oms_user_id
                username
                email
                full_name
                is_active
                oms_emp_code
                oms_organization_id
                oms_organization_name
                oms_ad_account
                oms_position_name
                oms_company_code
                oms_company_name
                oms_div_name
                oms_dep_name
                auth_group
            }
            updater {
                id
                created_by
                updated_by
                created_at
                updated_at
                deleted_at
                oms_user_id
                username
                email
                full_name
                is_active
                oms_emp_code
                oms_organization_id
                oms_organization_name
                oms_ad_account
                oms_position_name
                oms_company_code
                oms_company_name
                oms_div_name
                oms_dep_name
                auth_group
            }
        }
    }
`;

export const UPDATE_WORKFLOW = gql`
    mutation Update_workflow_definition($input: UpdateWorkflowDefinitionInput!) {
        update_workflow_definition(input: $input) {
            id
            created_by
            updated_by
            created_at
            updated_at
            deleted_at
            name
            bpmnXml
            status
            camunda_key
            camunda_id
            version
            workflow_instances {
                id
                created_by
                updated_by
                created_at
                updated_at
                deleted_at
                process_instance_id
                camunda_key
                business_key
                name
                status
                started_at
                ended_at
                form_data
                camunda_variables
            }
            workflow_steps {
                id
                step_key
                step_name
                step_order
            }
            workflow_forms {
                id
                form_id
                task_key
                task_name
                is_first_form
            }
        }
    }
`;

export const INACTIVATE_WORKFLOW_DEFINITION = gql`
    mutation InactivateWorkflowDefinition($id: String!) {
        inactivate_workflow_definition(id: $id) {
            id
            created_by
            updated_by
            created_at
            updated_at
            deleted_at
            name
            bpmnXml
            status
            camunda_key
            camunda_id
            version
            workflow_instances {
                id
                created_by
                updated_by
                created_at
                updated_at
                deleted_at
                process_instance_id
                camunda_key
                business_key
                name
                status
                started_at
                ended_at
                form_data
                camunda_variables
            }
            workflow_steps {
                id
                step_key
                step_name
                step_order
            }
            workflow_forms {
                id
                form_id
                task_key
                task_name
                is_first_form
            }
        }
    }
`;
export const REACTIVATE_WORKFLOW_DEFINITION = gql`
    mutation Reactivate_workflow_definition($id: String!) {
        reactivate_workflow_definition(id: $id) {
            id
            created_by
            updated_by
            created_at
            updated_at
            deleted_at
            name
            bpmnXml
            status
            camunda_key
            camunda_id
            version
            workflow_instances {
                id
                created_by
                updated_by
                created_at
                updated_at
                deleted_at
                process_instance_id
                camunda_key
                business_key
                name
                status
                started_at
                ended_at
                form_data
                camunda_variables
            }
            workflow_steps {
                id
                step_key
                step_name
                step_order
            }
            workflow_forms {
                id
                form_id
                task_key
                task_name
                is_first_form
            }
        }
    }
`;

export const ACTIVE_WORKFLOW_DEFINITION = gql`
    mutation Active_workflow_definition($id: String!) {
        active_workflow_definition(id: $id) {
            id
            created_by
            updated_by
            created_at
            updated_at
            deleted_at
            name
            bpmnXml
            status
            camunda_key
            camunda_id
            version
            workflow_steps {
                id
                step_key
                step_name
                step_order
            }
            workflow_instances {
                id
                created_by
                updated_by
                created_at
                updated_at
                deleted_at
                process_instance_id
                camunda_key
                business_key
                name
                status
                started_at
                ended_at
                form_data
                camunda_variables
            }
        }
    }
`;
export const DELETE_PROCESS = gql`
    mutation Remove_workflow_definition($id: String!) {
        remove_workflow_definition(id: $id)
    }
`;

export const CREATE_WORKFLOW = gql`
    mutation Create_workflow_definition($input: CreateWorkflowDefinitionInput!) {
        create_workflow_definition(input: $input) {
            id
            name
            bpmnXml
            status
            camunda_key
            camunda_id
            version
            created_by
            updated_by
            created_at
            updated_at
        }
    }
`;

export const WORKFLOW_INSTANCES_LIST = gql`
    query Workflow_instances_list($page: Int!, $limit: Int!, $filters: [String!], $sort: String, $search: String) {
        workflow_instances_list(body: { page: $page, limit: $limit, filters: $filters, sort: $sort, search: $search }) {
            totalCount
            totalPages
            currentPage
            data {
                id
                created_by
                updated_by
                created_at
                updated_at
                process_instance_id
                camunda_key
                business_key
                name
                status
                started_at
                ended_at
                form_data
                camunda_variables
                current_user_task_id
                type
                tracking_status
                sub_area_ids
                current_user_task {
                    id
                    created_by
                    updated_by
                    created_at
                    updated_at
                    deleted_at
                    workflow_instance_id
                    task_id
                    task_key
                    task_name
                    assignee
                    status
                    started_at
                    completed_at
                    completed_by
                    form_id
                    form_data
                    variables
                    workflow_step_id
                    workflow_step {
                        id
                        step_key
                        step_name
                        step_order
                    }
                }
                workflow_definition {
                    id
                    name
                    status
                    version
                }
                creator {
                    id
                    created_by
                    updated_by
                    created_at
                    updated_at
                    deleted_at
                    oms_user_id
                    username
                    email
                    full_name
                    is_active
                    oms_emp_code
                    oms_organization_id
                    oms_organization_name
                    oms_ad_account
                    oms_position_name
                    oms_company_code
                    oms_company_name
                    oms_div_name
                    oms_dep_name
                    auth_group
                }
            }
        }
    }
`;

export const WORKFLOW_INSTANCE_CREATE = gql`
    mutation CreateWorkflowInstance($input: CreateWorkflowInstanceInput!) {
        workflow_instances_create(input: $input) {
            id
            created_by
            updated_by
            created_at
            updated_at
            deleted_at
            process_instance_id
            camunda_key
            business_key
            name
            status
            started_at
            ended_at
            form_data
            camunda_variables
            workflow_definition {
                id
                created_by
                updated_by
                created_at
                updated_at
                deleted_at
                name
                bpmnXml
                status
                camunda_key
                camunda_id
                version
            }
            user_tasks {
                id
                created_by
                updated_by
                created_at
                updated_at
                deleted_at
                workflow_instance_id
                task_id
                task_key
                task_name
                assignee
                status
                started_at
                completed_at
                completed_by
                form_id
                form_data
                variables
                workflow_step_id
            }
            updater {
                id
                created_by
                updated_by
                created_at
                updated_at
                deleted_at
                oms_user_id
                username
                email
                full_name
                is_active
                oms_emp_code
                oms_organization_id
                oms_organization_name
                oms_ad_account
                oms_position_name
                oms_company_code
                oms_company_name
                oms_div_name
                oms_dep_name
                auth_group
            }
            creator {
                id
                created_by
                updated_by
                created_at
                updated_at
                deleted_at
                oms_user_id
                username
                email
                full_name
                is_active
                oms_emp_code
                oms_organization_id
                oms_organization_name
                oms_ad_account
                oms_position_name
                oms_company_code
                oms_company_name
                oms_div_name
                oms_dep_name
                auth_group
            }
        }
    }
`;

export const WORKFLOW_INSTANCE_DETAIL = gql`
    query WorkflowInstanceDetail($id: String!) {
        workflow_instances_detail(id: $id) {
            id
            created_by
            updated_by
            created_at
            updated_at
            deleted_at
            user_tasks {
                id
                order
                created_by
                updated_by
                created_at
                updated_at
                deleted_at
                workflow_instance_id
                task_id
                task_key
                task_name
                assignee
                status
                started_at
                completed_at
                completed_by
                form_id
                form_data
                variables
                workflow_step_id
                workflow_step {
                    id
                    step_key
                    step_name
                    step_order
                }
                workflow_instance {
                    id
                    created_by
                    updated_by
                    created_at
                    updated_at
                    deleted_at
                    process_instance_id
                    camunda_key
                    business_key
                    name
                    status
                    started_at
                    ended_at
                    form_data
                    camunda_variables
                    type
                    sub_area_ids
                    current_user_task_id
                }
                form {
                    id
                    created_by
                    updated_by
                    created_at
                    updated_at
                    deleted_at
                    name
                    schema
                    description
                    status
                    version
                    root_form_id
                }
                assigneeInfo {
                    id
                    created_by
                    updated_by
                    created_at
                    updated_at
                    deleted_at
                    oms_user_id
                    username
                    email
                    full_name
                    is_active
                    oms_emp_code
                    oms_organization_id
                    oms_organization_name
                    oms_ad_account
                    oms_position_name
                    oms_company_code
                    oms_company_name
                    oms_div_name
                    oms_dep_name
                    auth_group
                }
                userRoles {
                    id
                    created_by
                    updated_by
                    created_at
                    updated_at
                    deleted_at
                    is_gatekeeper_child
                    type
                    name
                    variable_name
                }
                user_zones {
                    id
                    created_by
                    updated_by
                    created_at
                    updated_at
                    deleted_at
                    type
                    name
                    code
                    parent_area_id
                    out_of_service
                }
                element_variable
                element_value
            }
            current_user_task {
                id
                created_by
                updated_by
                created_at
                updated_at
                deleted_at
                workflow_instance_id
                task_id
                task_key
                task_name
                assignee
                status
                started_at
                completed_at
                completed_by
                form_id
                form_data
                variables
                workflow_step_id
                workflow_step {
                    id
                    step_key
                    step_name
                    step_order
                }
            }
            workflow_definition {
                id
                created_by
                updated_by
                created_at
                updated_at
                deleted_at
                name
                bpmnXml
                status
                camunda_key
                camunda_id
                version
                workflow_forms {
                    id
                    form_id
                    task_key
                    task_name
                    is_first_form
                    form {
                        id
                        created_by
                        updated_by
                        created_at
                        updated_at
                        deleted_at
                        name
                        schema
                        description
                        status
                    }
                    workflow_definition {
                        id
                        created_by
                        updated_by
                        created_at
                        updated_at
                        deleted_at
                        name
                        bpmnXml
                        status
                        camunda_key
                        camunda_id
                        version
                    }
                }
                workflow_steps {
                    id
                    step_key
                    step_name
                    step_order
                }
                workflow_instances {
                    id
                    created_by
                    updated_by
                    created_at
                    updated_at
                    deleted_at
                    process_instance_id
                    camunda_key
                    business_key
                    name
                    status
                    started_at
                    ended_at
                    form_data
                    camunda_variables
                }
            }
            camunda_variables
            form_data
            ended_at
            started_at
            status
            name
            business_key
            camunda_key
            process_instance_id
            creator {
                id
                full_name
            }
            updater {
                id
                full_name
            }
        }
    }
`;

export const UPDATE_WORKFLOW_INSTANCE = gql`
    mutation UpdateWorkflowInstance($id: String!, $body: WorkflowInstanceUpdateInputDto!) {
        workflow_instances_update(id: $id, body: $body) {
            id
            created_by
            updated_by
            created_at
            updated_at
            deleted_at
            user_tasks {
                id
                created_by
                updated_by
                created_at
                updated_at
                deleted_at
                workflow_instance_id
                task_id
                task_key
                task_name
                assignee
                status
                started_at
                completed_at
                completed_by
                form_id
                form_data
                variables
                workflow_step_id
            }
            workflow_definition {
                id
                created_by
                updated_by
                created_at
                updated_at
                deleted_at
                name
                bpmnXml
                status
                camunda_key
                camunda_id
                version
                workflow_forms {
                    id
                    form_id
                    task_key
                    task_name
                    is_first_form
                    form {
                        id
                        created_by
                        updated_by
                        created_at
                        updated_at
                        deleted_at
                        name
                        schema
                        description
                        status
                    }
                    workflow_definition {
                        id
                        created_by
                        updated_by
                        created_at
                        updated_at
                        deleted_at
                        name
                        bpmnXml
                        status
                        camunda_key
                        camunda_id
                        version
                    }
                }
                workflow_steps {
                    id
                    step_key
                    step_name
                    step_order
                }
                workflow_instances {
                    id
                    created_by
                    updated_by
                    created_at
                    updated_at
                    deleted_at

                    process_instance_id
                    camunda_key
                    business_key
                    name
                    status
                    started_at
                    ended_at
                    form_data
                    camunda_variables
                }
            }
            camunda_variables
            form_data
            ended_at
            started_at
            status
            name
            business_key
            camunda_key
            process_instance_id
        }
    }
`;

export const USER_TASKS_BY_ASSIGNEE = gql`
    query user_tasks_by_assignee($page: Int!, $limit: Int!, $filters: [String!], $sort: String, $search: String) {
        user_tasks_by_assignee(body: { page: $page, limit: $limit, filters: $filters, sort: $sort, search: $search }) {
            totalCount
            totalPages
            currentPage
            data {
                id
                created_by
                updated_by
                created_at
                updated_at
                deleted_at
                workflow_instance_id
                task_id
                task_key
                task_name
                assignee
                status
                started_at
                completed_at
                completed_by
                form_id
                form_data
                variables
                workflow_step_id
                workflow_instance {
                    id
                    created_by
                    updated_by
                    created_at
                    updated_at
                    deleted_at
                    process_instance_id
                    camunda_key
                    business_key
                    name
                    status
                    started_at
                    ended_at
                    form_data
                    camunda_variables
                    current_user_task_id
                }
            }
        }
    }
`;

export const WORKFLOW_DEFINITION_LIST_ALL = gql`
    query Workflow_definition_list_all($filters: [String!], $sort: String, $search: String) {
        workflow_definition_list_all(body: { filters: $filters, sort: $sort, search: $search }) {
            id
            created_by
            updated_by
            created_at
            updated_at
            deleted_at
            name
            bpmnXml
            status
            camunda_key
            camunda_id
            version
            workflow_instances {
                id
                created_by
                updated_by
                created_at
                updated_at
                deleted_at
                process_instance_id
                camunda_key
                business_key
                name
                status
                started_at
                ended_at
                form_data
                camunda_variables
            }
            workflow_steps {
                id
                step_key
                step_name
                step_order
            }
            workflow_forms {
                id
                form_id
                task_key
                task_name
                is_first_form
                form {
                    id
                    created_by
                    updated_by
                    created_at
                    updated_at
                    deleted_at
                    name
                    schema
                    description
                    status
                }
            }
        }
    }
`;

export const CHANGE_REQUEST_LIST = gql`
    query Change_request_list($page: Int!, $limit: Int!, $filters: [String!], $sort: String, $search: String) {
        change_request_list(body: { page: $page, limit: $limit, filters: $filters, sort: $sort, search: $search }) {
            totalCount
            totalPages
            currentPage
            data {
                id
                created_by
                updated_by
                created_at
                updated_at
                process_instance_id
                camunda_key
                business_key
                name
                status
                started_at
                ended_at
                form_data
                camunda_variables
                current_user_task_id
                type
                tracking_status
                sub_area_ids
                current_user_task {
                    id
                    created_by
                    updated_by
                    created_at
                    updated_at
                    deleted_at
                    workflow_instance_id
                    task_id
                    task_key
                    task_name
                    assignee
                    status
                    started_at
                    completed_at
                    completed_by
                    form_id
                    form_data
                    variables
                    workflow_step_id
                    workflow_step {
                        id
                        step_key
                        step_name
                        step_order
                    }
                }
                workflow_definition {
                    id
                    name
                    status
                    version
                }
                creator {
                    id
                    created_by
                    updated_by
                    created_at
                    updated_at
                    deleted_at
                    oms_user_id
                    username
                    email
                    full_name
                    is_active
                    oms_emp_code
                    oms_organization_id
                    oms_organization_name
                    oms_ad_account
                    oms_position_name
                    oms_company_code
                    oms_company_name
                    oms_div_name
                    oms_dep_name
                    auth_group
                }
            }
        }
    }
`;

export const VALIDATE_BPMN = gql`
    mutation Validate_bpmn($bpmnXml: String!) {
        validate_bpmn(bpmnXml: $bpmnXml) {
            isValid
            errors
        }
    }
`;

export const WORKFLOW_INSTANCE_DASHBOARD = `
    query Workflow_instances_dashboard {
        workflow_instances_dashboard {
            in_progress
            completed
            cancelled
            overdue
            backlog
            draft
        }
    }
`;

export const CHANGE_REQUEST_DASHBOARD = `
    query Workflow_instances_auth_dashboard {
        workflow_instances_auth_dashboard {
            in_progress
            completed
            cancelled
            overdue
            backlog
            draft
        }
    }
`;
