import classNames from 'classnames';
import React from 'react';
import { ButtonType } from 'types/Form';
import { TaskStatus } from 'types/Task';
import { UserTaskEntity } from 'types/Workflow';
import { getStatusBadgeStep } from 'utils/common';
import { FORMAT_DATE, formatDateTime } from 'utils/date';

interface TableReviewProps {
    data: UserTaskEntity[];
}

export const TableReviewSubElementVariable = ({ data }: TableReviewProps) => {
    return (
        <div className={classNames('card tw-p-8 tw-max-w-[1000px] tw-mx-auto border tw-mb-8  tw-bg-[whitesmoke]')}>
            <div>
                <p className="tw-font-bold tw-mb-2  tw-max-w-[1000px]">Review Result</p>
                <div className="tw-overflow-auto  tw-max-w-[1000px] tw-bg-white">
                    <table className="tw-w-full tw-border tw-border-collapse">
                        <thead className="tw-bg-gray-100">
                            <tr>
                                <th className="tw-border tw-p-2 tw-text-center tw-w-[5%]">No</th>
                                <th className="tw-border tw-p-2 tw-w-[15%]">Approval Role</th>
                                <th className="tw-border tw-p-2 tw-w-[15%]">Zone Name</th>
                                <th className="tw-border tw-p-2 tw-w-[15%]">Reviewer Name</th>
                                <th className="tw-border tw-p-2 tw-w-[15%]">Updated On</th>
                                <th className="tw-border tw-p-2 tw-text-center tw-w-[10%]">Decision</th>
                                <th className="tw-border tw-p-2 tw-w-[25%]">Remark</th>
                            </tr>
                        </thead>
                        <tbody>
                            {data?.reduce((rows, data, idx) => {
                                const formFields = data?.form?.schema?.form_fields || [];
                                const hasApproveButton = formFields.some(
                                    (item) => item?.buttonType === ButtonType.APPROVE
                                );
                                if (!hasApproveButton) return rows;

                                const getKeyRemark = formFields.find((item) => item?.isRemark)?.key;

                                const remarkValue = getKeyRemark ? data?.form_data?.[getKeyRemark] : '';

                                const rowIndex = rows.length + 1;

                                rows.push(
                                    <tr key={idx}>
                                        <td className="tw-border tw-p-2 tw-text-center">{rowIndex}</td>
                                        <td className="tw-border tw-p-2">
                                            {data?.userRoles?.map((item) => item.name).join(', ')}
                                        </td>
                                        <td className="tw-border tw-p-2">
                                            {data?.user_zones?.map((item) => item.name).join(', ')}
                                        </td>
                                        <td className="tw-border tw-p-2">{data?.assigneeInfo?.full_name}</td>
                                        <td className="tw-border tw-p-2">
                                            {data?.status !== TaskStatus.IN_PROGRESS &&
                                                formatDateTime(data?.updated_at, FORMAT_DATE.SHOW_DATE_MINUTE)}
                                        </td>
                                        <td className="tw-border tw-p-2 tw-text-center">
                                            {getStatusBadgeStep(data?.status)}
                                        </td>
                                        <td className="tw-border tw-p-2">{remarkValue}</td>
                                    </tr>
                                );

                                return rows;
                            }, [] as JSX.Element[])}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    );
};
