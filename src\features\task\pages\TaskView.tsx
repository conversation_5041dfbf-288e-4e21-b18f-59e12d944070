import { <PERSON>, useParams } from 'react-router-dom';
import { Helmet } from 'react-helmet-async';
import { useTranslation } from 'react-i18next';
import ContentHeader from 'components/partials/ContentHeader';
import Spinner from 'components/partials/Spinner';
import { useGraphQLQuery } from 'hooks/useGraphQLQuery';
import { TaskDetailQuery, TaskStatus, TaskFile } from 'types/Task';
import { QUERY_KEY } from 'constants/common';
import { TASK_DETAIL } from 'services/TaskService';
import { keepPreviousData } from '@tanstack/react-query';
import { useAuthStore } from 'stores/authStore';
import FormFields from 'features/form/components/FormFields';
import { FormItem } from 'types/Form';
import { useEffect, useMemo, useState } from 'react';
import MOCDetailsModal from '../components/MOCDetailsModal';
import { MessageSquare, Paperclip } from 'react-feather';
import { useGraphQLMutation } from '../../../hooks/useGraphQLMutation';
import TaskComment, { ITaskCommentGroup, TaskCommentListQuery } from '../../../types/TaskComment';
import { TASK_COMMENT_CREATE, TASK_COMMENT_DELETE, TASK_COMMENTS_LIST } from '../../../services/TaskCommentService';
import { showToast } from '../../../utils/common';
import classNames from 'classnames';
import { IFile } from '../../../types/common/Item';
import { WorkflowInstanceStep, WorkflowStepsType } from '../../../types/Workflow';
import { urgencyValue } from './TaskEdit';

export const getWorkflowStepClasses = (
    item: WorkflowStepsType,
    stepsByTask: WorkflowInstanceStep[],
    isFullStepCompleted: boolean,
    skippedSteps: WorkflowStepsType[]
) => {
    // Kiểm tra xem item có thuộc skippedSteps không
    const isSkippedStep = skippedSteps.some((skipped) => skipped.id === item.id);

    // Tìm step đang thực hiện (completed_at = null)
    const currentStepByTask = stepsByTask.find((step) => !step.completed_at);
    const isCurrentStepInProgress = currentStepByTask?.workflow_step_id === item.id;

    // Kiểm tra xem item có trong stepsByTask và đã hoàn thành không
    const completedStepByTask = stepsByTask.find((step) => step.workflow_step_id === item.id && step.completed_at);
    const isCompletedStep = !!completedStepByTask;

    const circleClasses = classNames('tw-w-8 tw-h-8 tw-rounded-full tw-flex tw-items-center tw-justify-center', {
        // Ưu tiên cao nhất: skippedSteps → xám đậm
        'tw-bg-gray-500': isSkippedStep,
        // Step đang thực hiện → màu cam
        'tw-bg-[#ff9f43]': !isSkippedStep && isCurrentStepInProgress,
        // Step đã hoàn thành hoặc isFullStepCompleted = true → màu xanh success
        'tw-bg-success': !isSkippedStep && !isCurrentStepInProgress && (isCompletedStep || isFullStepCompleted),
        // Còn lại → màu mặc định
        'bg-light-secondary': !isSkippedStep && !isCurrentStepInProgress && !isCompletedStep && !isFullStepCompleted,
    });

    const textClasses = classNames('tw-text-center tw-font-medium', {
        // Tất cả trường hợp trên đều có chữ trắng, trừ trường hợp mặc định
        'tw-text-white': isSkippedStep || isCurrentStepInProgress || isCompletedStep || isFullStepCompleted,
    });

    return { circleClasses, textClasses };
};

const getStatusBadgeStep = (status: TaskStatus) => {
    switch (status) {
        case TaskStatus.IN_PROGRESS:
            return 'In Progress';
        case TaskStatus.APPROVED:
            return 'Approved';
        case TaskStatus.TERMINATED:
            return 'Terminated';
        case TaskStatus.REJECTED:
            return 'Rejected';
        case TaskStatus.SUBMITTED:
            return 'Submitted';
        case TaskStatus.DELETED:
            return 'Deleted';
        default:
            return '';
    }
};

interface FileAttachment {
    workflow_step_id: string;
    workflow_step_name: string;
    workflow_step_order: number;
    files: (TaskFile | IFile)[];
}

export default function TaskView() {
    const { t } = useTranslation();
    const { id } = useParams<{ id: string }>();
    const user = useAuthStore((state) => state.user);
    const [showMOCDetailsModal, setShowMOCDetailsModal] = useState(false);
    const [activeTab, setActiveTab] = useState('moc-details');
    const [isSendingComment, setIsSendingComment] = useState(false);
    const [workflowInstanceId, setWorkflowInstanceId] = useState('');
    const [fileLength, setFileLength] = useState(0);

    const { data, isLoading, refetch } = useGraphQLQuery<TaskDetailQuery>(
        [QUERY_KEY.TASKS_DETAIL, id],
        TASK_DETAIL,
        { id },
        '',
        {
            enabled: !!id,
            placeholderData: keepPreviousData,
        }
    );

    const urgent: string = useMemo(() => data?.user_task_detail?.workflow_instance?.form_data?.urgent ?? '', [data]);

    const fullSteps = useMemo(
        () => data?.user_task_detail?.workflow_step?.workflow_definition?.workflow_steps || [],
        [data]
    );

    const stepsByTask = useMemo(() => data?.user_task_detail?.workflow_instance?.workflow_instance_steps || [], [data]);

    const isFullStepCompleted = useMemo(() => {
        if (!fullSteps.length || !stepsByTask.length) return false;

        const lastStep = fullSteps[fullSteps.length - 1];
        const lastStepByTask = stepsByTask.find((item) => item.workflow_step_id === lastStep.id);

        return !!lastStepByTask && !!lastStepByTask.completed_at;
    }, [fullSteps, stepsByTask]);

    // Tìm các step bị skip
    const skippedSteps = useMemo(() => {
        if (!fullSteps.length || !stepsByTask.length) return [];

        // Lấy step hiện tại (step đang thực hiện - completed_at = null)
        const currentStepByTask = stepsByTask.find((step) => !step.completed_at);
        if (!currentStepByTask) return [];

        // Tìm step hiện tại trong fullSteps để lấy step_order
        const currentFullStep = fullSteps.find((step) => step.id === currentStepByTask.workflow_step_id);
        if (!currentFullStep) return [];

        // Lấy danh sách workflow_step_id từ stepsByTask
        const executedStepIds = stepsByTask.map((step) => step.workflow_step_id);

        // Tìm các step bị skip: có trong fullSteps, không có trong stepsByTask, và có step_order < currentStep
        const skipped = fullSteps.filter(
            (step) => step.step_order < currentFullStep.step_order && !executedStepIds.includes(step.id)
        );

        return skipped;
    }, [fullSteps, stepsByTask]);

    useEffect(() => {
        setWorkflowInstanceId(data?.user_task_detail?.workflow_instance.id || '');
    }, [data]);

    const {
        data: taskCommentData,
        isLoading: isCommentLoading,
        refetch: refetchComments,
    } = useGraphQLQuery<TaskCommentListQuery>(
        [QUERY_KEY.TASL_COMMENTS, workflowInstanceId],
        TASK_COMMENTS_LIST,
        {
            filters: [`userTask.workflow_instance_id:=(${workflowInstanceId})`],
            sort: 'created_at:ASC',
        },
        '',
        {
            enabled: !!workflowInstanceId,
            placeholderData: keepPreviousData,
        }
    );
    const taskComments = useMemo(() => taskCommentData?.task_comments_list || [], [taskCommentData]);

    const createComment = useGraphQLMutation<
        { task_comments_create: TaskComment },
        { user_task_id: string; content: string }
    >(TASK_COMMENT_CREATE, '', {
        onSuccess: () => {
            showToast(true, ['Comment created successfully']);
            refetchComments();
            refetch();
            setIsSendingComment(false);
        },
        onError: () => {
            setIsSendingComment(false);
        },
    });

    const deleteComment = useGraphQLMutation<{ task_comments_delete: boolean }, { id: string }>(
        TASK_COMMENT_DELETE,
        '',
        {
            onSuccess: () => {
                showToast(true, ['Comment deleted successfully']);
                refetchComments();
                refetch();
            },
        }
    );

    const groupedTaskComments = useMemo(() => {
        const grouped = taskComments.reduce((acc, comment) => {
            const stepId = comment.userTask.workflow_step.id;

            if (!acc[stepId]) {
                acc[stepId] = {
                    workflow_step_id: stepId,
                    workflow_step_name: comment.userTask.workflow_step.step_name,
                    workflow_step_order: comment.userTask.workflow_step.step_order,
                    comments: [],
                };
            }

            acc[stepId].comments.push(comment);
            return acc;
        }, {} as Record<string, ITaskCommentGroup>);

        return Object.values(grouped).sort((a, b) => a.workflow_step_order - b.workflow_step_order);
    }, [taskComments]);

    const getFormInitial = data?.user_task_detail?.form?.schema;

    const handleViewMOCDetails = () => {
        setActiveTab('moc-details');
        setShowMOCDetailsModal(true);
    };

    const handleViewComments = () => {
        setActiveTab('comments');
        setShowMOCDetailsModal(true);
    };

    const handleViewAttachments = () => {
        setActiveTab('attachments');
        setShowMOCDetailsModal(true);
    };

    const handleSendComment = (content: string) => {
        setIsSendingComment(true);
        createComment.mutate({ user_task_id: id!, content });
    };

    const handleDeleteComment = (commentId: string) => {
        deleteComment.mutate({ id: commentId });
    };

    const fileAttachments = useMemo(() => {
        if (!data?.user_task_detail?.workflow_instance) {
            return [];
        }

        const attachments: FileAttachment[] = [];
        const workflowInstance = data.user_task_detail.workflow_instance;

        const firstStep = workflowInstance.workflow_definition?.workflow_steps?.find((step) => step.step_order === 1);

        if (firstStep && workflowInstance.files && workflowInstance.files.length > 0) {
            attachments.push({
                workflow_step_id: firstStep.id,
                workflow_step_name: firstStep.step_name,
                workflow_step_order: firstStep.step_order,
                files: workflowInstance.files,
            });
        }

        if (workflowInstance.user_tasks && workflowInstance.user_tasks.length > 0) {
            const stepGroups: Record<
                string,
                {
                    workflow_step_id: string;
                    workflow_step_name: string;
                    workflow_step_order: number;
                    files: IFile[];
                }
            > = {};

            workflowInstance.user_tasks.forEach((userTask) => {
                if (userTask.workflow_step && userTask.files && userTask.files.length > 0) {
                    const stepId = userTask.workflow_step.id;

                    if (!stepGroups[stepId]) {
                        stepGroups[stepId] = {
                            workflow_step_id: stepId,
                            workflow_step_name: userTask.workflow_step.step_name,
                            workflow_step_order: userTask.workflow_step.step_order,
                            files: [],
                        };
                    }

                    stepGroups[stepId].files.push(...userTask.files);
                }
            });

            Object.values(stepGroups).forEach((group) => {
                attachments.push({
                    workflow_step_id: group.workflow_step_id,
                    workflow_step_name: group.workflow_step_name,
                    workflow_step_order: group.workflow_step_order,
                    files: group.files,
                });
            });
        }

        return attachments.sort((a, b) => a.workflow_step_order - b.workflow_step_order);
    }, [data?.user_task_detail?.workflow_instance]);

    useEffect(() => {
        const totalFiles = fileAttachments.reduce((total, attachment) => total + attachment.files.length, 0);
        setFileLength(totalFiles);
    }, [fileAttachments]);

    return (
        <>
            <Helmet>
                <title>{t('Task')}</title>
            </Helmet>
            <ContentHeader
                title={
                    <>
                        <Link to="/task">Task</Link>
                    </>
                }
                breadcrumbs={[
                    {
                        itemElement: (
                            <>
                                {data?.user_task_detail.task_name} (Request:{' '}
                                <Link to={`/workflowInstance/${data?.user_task_detail.workflow_instance.id}`}>
                                    <span className="text-primary tw-underline">
                                        {data?.user_task_detail.workflow_instance.business_key}
                                    </span>
                                </Link>
                                )
                            </>
                        ),
                    },
                ]}
            />
            <div className="tw-flex tw-items-center tw-gap-6">
                <div className="card tw-mb-0 tw-p-6 tw-w-full tw-flex tw-flex-row tw-justify-start tw-gap-2 tw-flex-wrap">
                    <div className="tw-flex tw-items-center tw-w-max">
                        <p className="tw-text-xl tw-font-medium">MOC Progress:</p>
                    </div>
                    {fullSteps.map((item, index) => (
                        <div className="tw-flex tw-flex-col tw-items-center tw-gap-2 tw-w-[120px]" key={item.step_key}>
                            {(() => {
                                const { circleClasses, textClasses } = getWorkflowStepClasses(
                                    item,
                                    stepsByTask,
                                    isFullStepCompleted,
                                    skippedSteps
                                );
                                return (
                                    <>
                                        <div className={circleClasses}>
                                            <p className={textClasses}>{index + 1}</p>
                                        </div>
                                        <p className="tw-max-w-[120px] tw-break-words tw-text-center">
                                            {item.step_name}
                                        </p>
                                    </>
                                );
                            })()}
                        </div>
                    ))}
                </div>
            </div>
            <div className="content-body">
                <div className="col-12">
                    {isLoading ? (
                        <Spinner />
                    ) : (
                        <div className="tw-w-full mt-2">
                            <div className="card tw-p-6">
                                <div className="content-body">
                                    <div className="card tw-mb-0">
                                        <div className="card-header tw-border-b tw-border-b-[#e6e6e8] tw-p-0 tw-mb-3">
                                            <div className="tw-w-full tw-flex tw-justify-between tw-items-center">
                                                <h2 className="card-title tw-py-2">MOC Overview</h2>
                                                <div className="tw-flex tw-items-center tw-gap-2">
                                                    <button
                                                        type="button"
                                                        className="btn btn-primary btn-sm tw-flex tw-items-center tw-gap-2 tw-h-[30px]"
                                                        onClick={handleViewComments}
                                                    >
                                                        <MessageSquare size={14} />
                                                        Comments ({taskCommentData?.task_comments_list?.length})
                                                    </button>
                                                    <button
                                                        type="button"
                                                        className="btn btn-primary btn-sm tw-flex tw-items-center tw-gap-2 tw-h-[30px]"
                                                        onClick={handleViewAttachments}
                                                    >
                                                        <Paperclip size={14} />
                                                        Attachments ({fileLength})
                                                    </button>
                                                    <button
                                                        type="button"
                                                        className="btn btn-primary btn-sm tw-flex tw-items-center tw-gap-2 tw-h-[30px]"
                                                        onClick={handleViewMOCDetails}
                                                    >
                                                        View MOC details
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                        <div className="row">
                                            <div className="col-md-6 mb-1">
                                                <label className="form-label tw-text-[14px]" htmlFor="originator">
                                                    Request Change Title
                                                </label>
                                                <input
                                                    id="originator"
                                                    type="text"
                                                    className={`form-control`}
                                                    disabled
                                                    value={
                                                        data?.user_task_detail?.workflow_instance?.form_data
                                                            ?.request_title
                                                    }
                                                />
                                            </div>
                                            <div className="col-md-6 mb-1">
                                                <label className="form-label tw-text-[14px]" htmlFor="originator">
                                                    Type
                                                </label>
                                                <input
                                                    id="originator"
                                                    type="text"
                                                    className="form-control tw-capitalize"
                                                    disabled
                                                    value={
                                                        data?.user_task_detail?.workflow_instance?.form_data
                                                            ?.type_of_moc
                                                    }
                                                />
                                            </div>
                                            <div className="col-md-6 mb-1">
                                                <label className="form-label tw-text-[14px]" htmlFor="originator">
                                                    Originator
                                                </label>
                                                <input
                                                    id="originator"
                                                    type="text"
                                                    className={`form-control`}
                                                    disabled
                                                    value={
                                                        data?.user_task_detail?.workflow_instance?.creator?.full_name
                                                    }
                                                />
                                            </div>
                                            <div className="col-md-6 mb-1">
                                                <label className="form-label tw-text-[14px]" htmlFor="originator">
                                                    Urgency
                                                </label>
                                                <input
                                                    id="originator"
                                                    type="text"
                                                    className="form-control tw-capitalize"
                                                    disabled
                                                    value={urgencyValue[urgent] ?? ''}
                                                />
                                            </div>
                                            <div className="col-md-6 mb-1">
                                                <label className="form-label tw-text-[14px]" htmlFor="originator">
                                                    Overall Status
                                                </label>
                                                <input
                                                    id="originator"
                                                    type="text"
                                                    className={`form-control`}
                                                    disabled
                                                    value={getStatusBadgeStep(
                                                        data?.user_task_detail?.status as TaskStatus
                                                    )}
                                                />
                                            </div>
                                            {urgent !== 'non_required' && (
                                                <div className="col-md-6 mb-1">
                                                    <label className="form-label tw-text-[14px]" htmlFor="originator">
                                                        Urgency Reason
                                                    </label>
                                                    <textarea
                                                        className="form-control tw-min-h-[40px] tw-max-h-[120px]"
                                                        disabled
                                                        value={
                                                            data?.user_task_detail?.workflow_instance?.form_data
                                                                ?.urgency_reason
                                                        }
                                                    />
                                                </div>
                                            )}
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div className="card tw-p-10">
                                <div className="content-body">
                                    {getFormInitial && (
                                        <FormFields
                                            dataUserTaskDetail={data?.user_task_detail}
                                            schemaForm={getFormInitial as FormItem}
                                            initialData={data?.user_task_detail?.form_data}
                                            dataPlanAndAreaUnit={
                                                data?.user_task_detail?.workflow_instance?.form_data
                                                    ?.area_of_implementation
                                            }
                                            listDataForm={data?.user_task_detail?.workflow_instance?.user_tasks}
                                            assignee={data?.user_task_detail?.assignee}
                                            isDisableForm={true}
                                        />
                                    )}
                                </div>
                            </div>
                        </div>
                    )}
                </div>
            </div>
            {showMOCDetailsModal && (
                <MOCDetailsModal
                    show={showMOCDetailsModal}
                    changeShow={setShowMOCDetailsModal}
                    taskComments={taskCommentData?.task_comments_list || []}
                    activeTab={activeTab}
                    onSendComment={handleSendComment}
                    onDeleteComment={handleDeleteComment}
                    isSendingComment={isSendingComment}
                    userTaskDetail={data?.user_task_detail}
                    groupedTaskComments={groupedTaskComments}
                    fileAttachments={fileAttachments}
                    fileLength={fileLength}
                    isEdit={false}
                />
            )}
        </>
    );
}
