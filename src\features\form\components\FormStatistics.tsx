import { useTranslation } from 'react-i18next';
import { FormDashboardQuery } from '../../../types/Form';

interface IProps {
    formDashboard?: FormDashboardQuery;
    handleChangeStatus: (status: string | null) => void;
}

export default function FormStatistics({ formDashboard, handleChangeStatus }: Readonly<IProps>) {
    const { t } = useTranslation();

    return (
        <div className="row">
            <div className="col-lg-3 col-sm-6 col-12">
                <div className="card">
                    <button
                        type="button"
                        className="card-header tw-w-full tw-text-left"
                        onClick={() => handleChangeStatus(null)}
                    >
                        <div>
                            <h2 className="fw-bolder mb-0">{formDashboard?.form_dashboard?.total}</h2>
                            <p className="card-text">{t('Total Form')}</p>
                        </div>
                        <div className="avatar tw-bg-[#00AFF0] p-50 m-0">
                            <div className="avatar-content">
                                <i className="font-medium-5 bi bi-list-task"></i>
                            </div>
                        </div>
                    </button>
                </div>
            </div>
            <div className="col-lg-3 col-sm-6 col-12">
                <div className="card">
                    <button
                        type="button"
                        className="card-header tw-w-full tw-text-left"
                        onClick={() => handleChangeStatus('DRAFT')}
                    >
                        <div>
                            <h2 className="fw-bolder mb-0">{formDashboard?.form_dashboard?.draft}</h2>
                            <p className="card-text">{t('Draft')}</p>
                        </div>
                        <div className="avatar tw-bg-warning-400 p-50 m-0">
                            <div className="avatar-content">
                                <i className="font-medium-5 bi bi-hourglass-split"></i>
                            </div>
                        </div>
                    </button>
                </div>
            </div>
            <div className="col-lg-3 col-sm-6 col-12">
                <div className="card">
                    <button
                        type="button"
                        className="card-header tw-w-full tw-text-left"
                        onClick={() => handleChangeStatus('ACTIVE')}
                    >
                        <div>
                            <h2 className="fw-bolder mb-0">{formDashboard?.form_dashboard?.activated}</h2>
                            <p className="card-text">{t('Active')}</p>
                        </div>
                        <div className="avatar tw-bg-success-400 p-50 m-0">
                            <div className="avatar-content">
                                <i className="font-medium-5 bi bi-check-circle"></i>
                            </div>
                        </div>
                    </button>
                </div>
            </div>
            <div className="col-lg-3 col-sm-6 col-12">
                <div className="card">
                    <button
                        type="button"
                        className="card-header tw-w-full tw-text-left"
                        onClick={() => handleChangeStatus('INACTIVE')}
                    >
                        <div>
                            <h2 className="fw-bolder mb-0">{formDashboard?.form_dashboard?.inactivated}</h2>
                            <p className="card-text">{t('Inactive')}</p>
                        </div>
                        <div className="avatar tw-bg-gray-400 p-50 m-0">
                            <div className="avatar-content">
                                <i className="font-medium-5 bi bi-check-circle"></i>
                            </div>
                        </div>
                    </button>
                </div>
            </div>
        </div>
    );
}
