import { useQueryClient } from '@tanstack/react-query';
import Role from '../../../types/Role';
import { Trash2 } from 'react-feather';
import { QUERY_KEY } from '../../../constants/common';
import { useMemo } from 'react';

interface IProps {
    role?: Role;
    onOpenAssignUser?: () => void;
    onDeleteUser?: (userAreaRoleId: string) => void;
}

const RoleSystemWide = ({ role, onOpenAssignUser, onDeleteUser }: Readonly<IProps>) => {
    const queryClient = useQueryClient();

    const users = useMemo(
        () => role?.userAreaRoles.sort((a, b) => a.user.full_name.localeCompare(b.user.full_name)) || [],
        [role]
    );

    const handleDeleteUser = (userAreaRoleId: string) => {
        onDeleteUser?.(userAreaRoleId);
    };

    const handleAddNewUser = () => {
        // Refetch USER_LIST_PAGINATE để lấy danh sách users mới nhất
        queryClient.invalidateQueries({
            queryKey: [QUERY_KEY.USER_ACCOUNT_LIST],
        });

        if (onOpenAssignUser) {
            onOpenAssignUser();
        }
    };

    return (
        <div className="border rounded shadow bg-white min-vh-50 p-1">
            <div className="d-flex justify-content-between align-items-center mb-1">
                <div className="fw-semibold fs-5">All PSSR Leaders</div>
                <button className="btn btn-primary btn-sm" onClick={handleAddNewUser}>
                    Add new PSSR Leader
                </button>
            </div>

            <div className="table-responsive">
                <table className="table table-bordered table-sm align-middle mb-0">
                    <thead className="table-light">
                        <tr>
                            <th>User Name</th>
                            <th className="text-center" style={{ width: 100 }}>
                                Action
                            </th>
                        </tr>
                    </thead>
                    <tbody>
                        {users.map((userAreaRole, idx) => (
                            <tr key={userAreaRole.id}>
                                <td>{userAreaRole.user.full_name}</td>
                                <td className="text-center">
                                    <button
                                        className="btn btn-sm btn-outline-danger"
                                        onClick={() => handleDeleteUser(userAreaRole.id)}
                                        title="Delete user"
                                    >
                                        <Trash2 size={16} />
                                    </button>
                                </td>
                            </tr>
                        ))}
                    </tbody>
                </table>
            </div>
        </div>
    );
};

export default RoleSystemWide;
