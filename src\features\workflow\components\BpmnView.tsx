/* eslint-disable react-hooks/exhaustive-deps */
import { useEffect, useRef, useState, useMemo, useCallback } from 'react';
import BpmnJS from 'bpmn-js/lib/Modeler';
import camundaModdlePackage from 'camunda-bpmn-moddle/resources/camunda';
import {
    BpmnPropertiesPanelModule,
    BpmnPropertiesProviderModule,
    CamundaPlatformPropertiesProviderModule,
    ElementTemplatesPropertiesProviderModule,
} from 'bpmn-js-properties-panel';
import 'bpmn-js-properties-panel/dist/assets/properties-panel.css';
import 'bpmn-js/dist/assets/diagram-js.css';
import 'bpmn-js/dist/assets/bpmn-js.css';
import 'bpmn-js/dist/assets/bpmn-font/css/bpmn-embedded.css';
import 'bpmn.css';

import { emptyDiagram } from './emptyDiagram';
import { useGraphQLQuery } from 'hooks/useGraphQLQuery';
import { QUERY_KEY } from 'constants/common';
import { CREATE_WORKFLOW, GET_WORKFLOW, UPDATE_WORKFLOW, VALIDATE_BPMN } from 'services/WorkflowService';
import { keepPreviousData } from '@tanstack/react-query';
import { getStatusBadgeWorkflowList, showToast } from 'utils/common';
import { WorkflowDetailQuery, WorkflowStatus } from 'types/Workflow';
import classNames from 'classnames';
import { useGraphQLMutation } from 'hooks/useGraphQLMutation';
import { useNavigate } from 'react-router-dom';
import Spinner from 'components/partials/Spinner';
import { useAppStore } from 'stores/appStore';
import { FormQuery } from 'types/Form';
import { FORM_LIST } from 'services/FormService';
import { FORMAT_DATE, formatDateTime } from 'utils/date';
import {
    CustomPaletteProvider,
    extractTopLevelSubProcessesInOrder,
    findFirstUserTaskElementWithFormKeyOrEmptyUsingRegistry,
} from 'utils/bpmn';
import { useAuthStore } from '../../../stores/authStore';
import { AuthGroups } from '../../../types/User';

(CustomPaletteProvider as any).$inject = ['palette', 'create', 'elementFactory', 'translate'];
interface BpmnViewProps {
    id: string;
}

const BpmnView = ({ id }: BpmnViewProps) => {
    const navigate = useNavigate();
    const isLoadingApp = useAppStore((state) => state.isLoadingApp);
    const setIsLoadingApp = useAppStore((state) => state.setIsLoadingApp);
    const containerRef = useRef<HTMLDivElement>(null);
    const modelerRef = useRef<any>(null);
    const panelRef = useRef<HTMLDivElement | null>(null);
    const currentUser = useAuthStore((state) => state.user);
    const isSuperAdmin = useMemo(() => currentUser?.auth_group === AuthGroups.SUPER_ADMIN, [currentUser?.auth_group]);

    const selectedElementRef = useRef<any>(null);

    const { data: dataForm } = useGraphQLQuery<FormQuery>(
        [QUERY_KEY.FORM_LIST],
        FORM_LIST,
        {
            page: 1,
            limit: 10000,
            search: undefined,
            sort: undefined,
            filters: ['status:=(ACTIVE)'],
        },
        '',
        {
            placeholderData: keepPreviousData,
        }
    );

    const newOptionFormKey = useMemo(
        () => [
            { value: '', label: 'None', version: '' },
            ...(dataForm?.form_list.data.map((item) => ({
                value: item.id,
                label: item.name,
                version: item.version,
            })) ?? []),
        ],
        [dataForm?.form_list.data]
    );

    const { data, isLoading, isRefetching, isSuccess } = useGraphQLQuery<WorkflowDetailQuery>(
        [QUERY_KEY.WORKFLOWS_DETAIL, id],
        GET_WORKFLOW,
        { id },
        '',
        {
            enabled: !!id,
            placeholderData: keepPreviousData,
        }
    );
    const [nameProcess, setNameProcess] = useState(data?.workflow_definition_detail.name || '');
    const [nameError, setNameError] = useState(false);

    const onChangeName = (value: string) => {
        setNameProcess(value);
        value && setNameError(false);
    };

    useEffect(() => {
        setIsLoadingApp(false);
    }, [isSuccess]);

    const updateProcessMutation = useGraphQLMutation(UPDATE_WORKFLOW, '', {
        onSuccess: () => {
            showToast(true, ['Update workflow successfully']);
            navigate('/workflow');
        },
        onSettled: () => {
            setIsLoadingApp(false);
        },
    });
    const createProcessMutation = useGraphQLMutation(CREATE_WORKFLOW, '', {
        onSuccess: () => {
            showToast(true, ['Create workflow successfully']);
            navigate('/workflow');
        },
        onSettled: () => {
            setIsLoadingApp(false);
        },
    });

    const injectHTMLToFormType = useCallback(
        (assignFormKey: (formKey: string) => void) => {
            const container = document.querySelector('.bio-properties-panel-entry[data-entry-id="formType"]');

            if (container && !container.querySelector('.custom-form-type')) {
                const wrapper = document.createElement('div');
                wrapper.className = 'custom-form-type';
                wrapper.innerHTML = `
                <select class="custom-form-select" style="
                    width: 100%;
                    height: 38px;
                    border: 1px solid hsl(225, 10%, 75%);
                    border-radius: 2px;
                    background: hsl(225, 10%, 97%);
                    padding: 0 8px;
                    font-size: 14px;
                    color: #22242A;
                    outline: none;
                    cursor: pointer;
                ">
                    ${newOptionFormKey
                        .map(
                            (option) =>
                                `<option value="${option.value}">${option.label} ${
                                    option.value && `(version: ${option.version})`
                                }</option>`
                        )
                        .join('')}
                </select>
            `;

                container.appendChild(wrapper);

                const select = wrapper.querySelector('.custom-form-select') as HTMLSelectElement;

                const updateSelectedValue = () => {
                    const element = selectedElementRef.current;
                    const formKey = element?.businessObject?.get('camunda:formKey') || '';
                    select.value = formKey;
                };

                updateSelectedValue();

                select.addEventListener('change', (e) => {
                    const target = e.target as HTMLSelectElement;
                    assignFormKey(target.value);
                });

                select.addEventListener('focus', () => {
                    select.style.borderColor = '#00AFF0';
                });

                select.addEventListener('blur', () => {
                    select.style.borderColor = 'hsl(225, 10%, 75%)';
                });

                select.addEventListener('mouseenter', () => {
                    if (document.activeElement !== select) {
                        select.style.borderColor = '#00AFF0';
                    }
                });

                select.addEventListener('mouseleave', () => {
                    if (document.activeElement !== select) {
                        select.style.borderColor = 'hsl(225, 10%, 75%)';
                    }
                });

                (wrapper as any).updateSelectedValue = updateSelectedValue;
            } else if (container) {
                const wrapper = container.querySelector('.custom-form-type') as any;
                if (wrapper && wrapper.updateSelectedValue) {
                    wrapper.updateSelectedValue();
                }
            }
        },
        [newOptionFormKey]
    );

    useEffect(() => {
        const modeler = new BpmnJS({
            container: containerRef.current!,
            propertiesPanel: {
                parent: panelRef.current,
            },
            keyboard: {
                bindTo: document,
            },
            additionalModules: [
                BpmnPropertiesPanelModule,
                BpmnPropertiesProviderModule,
                CamundaPlatformPropertiesProviderModule,
                ElementTemplatesPropertiesProviderModule,
                {
                    __init__: ['customPaletteProvider'],
                    customPaletteProvider: ['type', CustomPaletteProvider],
                },
            ],
            moddleExtensions: {
                camunda: camundaModdlePackage,
            },
        });

        modelerRef.current = modeler;

        // Tắt FORM ở start event

        modeler.on('selection.changed', ({ newSelection }: { newSelection: any }) => {
            const element = newSelection[0];
            const panelRoot = document.querySelector('.bio-properties-panel');

            if (!panelRoot) return;

            const isStartEvent = element?.type === 'bpmn:StartEvent';

            panelRoot.classList.toggle('start-event-active', isStartEvent);
        });

        // Tắt FORM ở start event

        modeler.on('shape.added', function (event: any) {
            const element = event.element;

            if (element.type === 'bpmn:UserTask') {
                selectedElementRef.current = element;

                setTimeout(() => {
                    injectHTMLToFormType(assignFormKey);
                }, 0);
            }
        });

        modeler.on('element.click', function (event: any) {
            const element = event.element;

            if (element.type === 'bpmn:UserTask') {
                selectedElementRef.current = element;

                setTimeout(() => {
                    injectHTMLToFormType(assignFormKey);
                }, 0);
            } else {
                selectedElementRef.current = null;
            }
        });

        modeler.on('shape.move.start', function (event: any) {
            const context = event.context;
            const shape = context.shape;

            if (shape.type === 'bpmn:UserTask') {
                selectedElementRef.current = shape;

                setTimeout(() => {
                    injectHTMLToFormType(assignFormKey);
                }, 0);
            }
        });

        modeler.on('shape.move.end', function (event: any) {
            const context = event.context;
            const shape = context.shape;

            if (shape.type === 'bpmn:UserTask') {
                selectedElementRef.current = shape;

                setTimeout(() => {
                    injectHTMLToFormType(assignFormKey);
                }, 0);
            }
        });

        modeler.on('propertiesPanel.updated', () => {
            const element = selectedElementRef.current;
            const formKey = element?.businessObject?.get('camunda:formKey') || '';

            setTimeout(() => {
                formKey && injectHTMLToFormType(assignFormKey);
            }, 0);
        });

        const container = containerRef.current;
        const importXML = () => {
            modeler
                .importXML(data?.workflow_definition_detail.bpmnXml || emptyDiagram)
                .then(() => {
                    modeler.get('canvas').resized();
                })
                .catch((err: any) => {
                    // console.error('Import lỗi:', err);
                });
        };

        if (container) {
            const observer = new ResizeObserver(([entry]) => {
                const { width, height } = entry.contentRect;
                if (width > 0 && height > 0) {
                    observer.disconnect();
                    importXML();
                }
            });
            observer.observe(container);
        }

        setNameProcess(data?.workflow_definition_detail.name || '');

        return () => {
            modeler.destroy();
        };
    }, [data?.workflow_definition_detail.bpmnXml, isLoadingApp, isRefetching, isLoading, injectHTMLToFormType]);

    const assignFormKey = (key: string) => {
        const element = selectedElementRef.current;
        if (!element) return;

        const modeling = modelerRef.current?.get('modeling');
        if (!modeling) return;

        // Gán formKey mới cho userTask hiện tại
        modeling.updateProperties(element, {
            'camunda:formKey': key,
        });
    };

    const onSaveWorkflow = async () => {
        const { xml } = await modelerRef.current.saveXML({ format: true });
        const fixedXml = xml
            .replace(/isExecutable="false"/g, 'isExecutable="true"')
            .replace(/<process([^>]*?)>/g, (match: any, attrs: string) => {
                // Nếu đã có historyTimeToLive thì giữ nguyên
                if (/historyTimeToLive=/.test(attrs)) return match;
                return `<process${attrs} historyTimeToLive="60">`;
            });

        const elementRegistry = modelerRef.current.get('elementRegistry');
        const startEvent = elementRegistry.find((el: any) => el.type === 'bpmn:StartEvent');

        const subProcesses = extractTopLevelSubProcessesInOrder(fixedXml || '');

        if (startEvent) {
            const firstUserTask = findFirstUserTaskElementWithFormKeyOrEmptyUsingRegistry(
                startEvent.id,
                elementRegistry
            );

            const formKey =
                firstUserTask?.businessObject?.$attrs?.['camunda:formKey'] ||
                firstUserTask?.businessObject?.get?.('camunda:formKey') ||
                '';
            const firstUserTaskId = firstUserTask?.id;
            if (nameProcess) {
                setIsLoadingApp(true);
                updateProcessMutation.mutate({
                    input: {
                        id: id,
                        name: nameProcess,
                        bpmnXml: fixedXml || '',
                        first_user_task: { id: firstUserTaskId, form_id: formKey },
                        sub_processes: subProcesses,
                    },
                });
            } else {
                setNameError(true);
            }
        }
    };

    const onCreate = async () => {
        const { xml } = await modelerRef.current.saveXML({ format: true });
        const fixedXml = xml
            .replace(/isExecutable="false"/g, 'isExecutable="true"')
            .replace(/<process([^>]*?)>/g, (match: any, attrs: string) => {
                // Nếu đã có historyTimeToLive thì giữ nguyên
                if (/historyTimeToLive=/.test(attrs)) return match;
                return `<process${attrs} historyTimeToLive="60">`;
            });

        const elementRegistry = modelerRef.current.get('elementRegistry');
        const startEvent = elementRegistry.find((el: any) => el.type === 'bpmn:StartEvent');

        const subProcesses = extractTopLevelSubProcessesInOrder(fixedXml || '');

        if (startEvent) {
            const firstUserTask = findFirstUserTaskElementWithFormKeyOrEmptyUsingRegistry(
                startEvent.id,
                elementRegistry
            );

            const formKey =
                firstUserTask?.businessObject?.$attrs?.['camunda:formKey'] ||
                firstUserTask?.businessObject?.get?.('camunda:formKey') ||
                '';
            const firstUserTaskId = firstUserTask?.id;
            if (nameProcess) {
                setIsLoadingApp(true);
                createProcessMutation.mutate({
                    input: {
                        name: nameProcess,
                        bpmnXml: fixedXml || '',
                        first_user_task: { id: firstUserTaskId, form_id: formKey },
                        sub_processes: subProcesses,
                    },
                });
            } else {
                setNameError(true);
            }
        }
    };

    const validateBPMNMutation = useGraphQLMutation<
        { validate_bpmn: { isValid: boolean; errors: string[] } },
        { bpmnXml: string }
    >(VALIDATE_BPMN, '', {
        onSuccess: (data) => {
            if (data?.validate_bpmn?.isValid) {
                showToast(true, ['Validate workflow successfully']);
            } else {
                data?.validate_bpmn?.errors.forEach((error: string) => {
                    showToast(false, [error]);
                });
            }
        },
    });

    const onValidate = async () => {
        const { xml } = await modelerRef.current.saveXML({ format: true });
        const fixedXml = xml
            .replace(/isExecutable="false"/g, 'isExecutable="true"')
            .replace(/<process([^>]*?)>/g, (match: any, attrs: string) => {
                // Nếu đã có historyTimeToLive thì giữ nguyên
                if (/historyTimeToLive=/.test(attrs)) return match;
                return `<process${attrs} historyTimeToLive="60">`;
            });
        validateBPMNMutation.mutate({ bpmnXml: fixedXml || '' });
    };

    return (
        <div className="tw-flex tw-h-[calc(100vh-200px)]">
            <div className="tw-flex tw-flex-1 tw-flex-col">
                {(isLoading || isRefetching || isLoadingApp) && <Spinner />}
                {!isLoading && !isRefetching && !isLoadingApp && (
                    <>
                        <div className={classNames('tw-border-b tw-border-b-[#e6e6e8] tw-p-4 tw-bg-white')}>
                            <div className="tw-flex tw-flex-col  tw-gap-6">
                                <div className="tw-flex tw-gap-4 tw-justify-between">
                                    <div className="col-md-4">
                                        <label htmlFor="name" className="tw-font-medium">
                                            Workflow Name
                                        </label>
                                        <input
                                            id="name"
                                            type="text"
                                            className={`form-control  tw-w-[300px] ${nameError ? 'is-invalid' : ''}`}
                                            placeholder="Name"
                                            onChange={(e) => onChangeName(e.target.value)}
                                            value={nameProcess}
                                            disabled={!isSuperAdmin}
                                            readOnly={!isSuperAdmin}
                                        />{' '}
                                    </div>
                                    <div className="tw-flex tw-gap-4 tw-flex-wrap">
                                        {isSuperAdmin && (
                                            <>
                                                {(data?.workflow_definition_detail.status === WorkflowStatus.DRAFT ||
                                                    data?.workflow_definition_detail.status ===
                                                        WorkflowStatus.INACTIVE) && (
                                                    <button
                                                        type="button"
                                                        className="btn btn-primary waves-effect waves-light tw-h-[38px]"
                                                        onClick={onValidate}
                                                    >
                                                        Validate Workflow
                                                    </button>
                                                )}
                                                {data?.workflow_definition_detail.status === WorkflowStatus.DRAFT && (
                                                    <button
                                                        type="button"
                                                        className="btn btn-primary waves-effect waves-light tw-h-[38px]"
                                                        onClick={onSaveWorkflow}
                                                    >
                                                        Save Draft
                                                    </button>
                                                )}
                                            </>
                                        )}
                                        {(data?.workflow_definition_detail.status === WorkflowStatus.ACTIVE ||
                                            data?.workflow_definition_detail.status === WorkflowStatus.INACTIVE) && (
                                            <button
                                                type="button"
                                                className="btn btn-primary waves-effect waves-light  tw-h-[38px]"
                                                onClick={onSaveWorkflow}
                                            >
                                                Save New Version
                                            </button>
                                        )}
                                        {!id && (
                                            <button
                                                type="button"
                                                className="btn btn-primary waves-effect waves-light tw-h-[38px]"
                                                onClick={onCreate}
                                            >
                                                Create
                                            </button>
                                        )}
                                    </div>
                                </div>
                                {id && (
                                    <div className="tw-grid sm:tw-grid-cols-2 lg:tw-grid-cols-2 xl:tw-grid-cols-4 tw-gap-4 tw-w-full">
                                        <div className="tw-flex tw-items-start tw-gap-2 tw-border-r">
                                            <p className="tw-font-medium">Status:</p>
                                            {data?.workflow_definition_detail.status &&
                                                getStatusBadgeWorkflowList(data?.workflow_definition_detail.status)}
                                        </div>
                                        <div className="tw-flex tw-items-start tw-gap-2 tw-col-span-1">
                                            <p className="tw-font-medium">Version:</p>
                                            <p>{data?.workflow_definition_detail.version}</p>
                                        </div>{' '}
                                        <div className="tw-col-span-2 tw-hidden xl:tw-block" />
                                        <div className="tw-flex tw-items-start tw-gap-2 tw-col-span-1  tw-border-r">
                                            <p className="tw-font-medium tw-flex-shrink-0">Created By:</p>
                                            <p>{data?.workflow_definition_detail?.creator?.full_name}</p>
                                        </div>
                                        <div className="tw-flex tw-items-start tw-gap-2 tw-col-span-1 tw-border-r-0 xl:tw-border-r">
                                            <p className="tw-font-medium  tw-flex-shrink-0">Created At:</p>
                                            <p>
                                                {formatDateTime(
                                                    data?.workflow_definition_detail.created_at || '',
                                                    FORMAT_DATE.SHOW_DATE_MINUTE
                                                )}
                                            </p>
                                        </div>
                                        <div className="tw-flex tw-items-start tw-gap-2 tw-col-span-1 tw-border-r">
                                            <p className="tw-font-medium  tw-flex-shrink-0">Last Updated By:</p>
                                            <p>{data?.workflow_definition_detail?.updater?.full_name}</p>
                                        </div>
                                        <div className="tw-flex tw-items-start tw-gap-2 tw-col-span-1">
                                            <p className="tw-font-medium  tw-flex-shrink-0">Last Updated At:</p>
                                            <p>
                                                {data?.workflow_definition_detail.updated_at
                                                    ? formatDateTime(
                                                          data?.workflow_definition_detail.updated_at || '',
                                                          FORMAT_DATE.SHOW_DATE_MINUTE
                                                      )
                                                    : formatDateTime(
                                                          data?.workflow_definition_detail.created_at || '',
                                                          FORMAT_DATE.SHOW_DATE_MINUTE
                                                      )}
                                            </p>
                                        </div>
                                    </div>
                                )}
                            </div>
                        </div>
                        <div className="tw-flex tw-h-[100vh]">
                            <div ref={containerRef} className="tw-flex-1 tw-bg-white" />
                            <div ref={panelRef} className="tw-w-[300px] tw-border-l tw-border-l-[#e6e6e8]" />
                        </div>
                    </>
                )}
            </div>
        </div>
    );
};

export default BpmnView;
