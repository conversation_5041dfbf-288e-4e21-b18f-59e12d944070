import { gql } from 'graphql-request';

export const TASK_COMMENTS_LIST = gql`
    query Task_comments_list($filters: [String!], $sort: String, $search: String) {
        task_comments_list(body: { search: $search, filters: $filters, sort: $sort }) {
            id
            user_task_id
            content
            userTask {
                workflow_step {
                    id
                    step_key
                    step_name
                    step_order
                }
                userRoles {
                    id
                    name
                    type
                    is_gatekeeper_child
                    variable_name
                }
                id
            }
            creator {
                full_name
            }
            created_at
            created_by
        }
    }
`;

export const TASK_COMMENT_CREATE = gql`
    mutation Task_comments_create($user_task_id: String!, $content: String!) {
        task_comments_create(body: { user_task_id: $user_task_id, content: $content }) {
            id
        }
    }
`;

export const TASK_COMMENT_DELETE = gql`
    mutation Task_comments_delete($id: String!) {
        task_comments_delete(id: $id)
    }
`;
