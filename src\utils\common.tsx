import { TFunction } from 'i18next';
import { find, get, isEmpty } from 'lodash';
import { toast, ToastOptions } from 'react-toastify';
import { ItemParam, SelectOption, SelectOptionString } from 'types/common/Item';
import { DataList, Paging } from '../types/common';
import { COMMON_MESSAGE } from '../constants/common';
import { Task, TaskStatus } from 'types/Task';
import { TrackingStatus, UserTaskEntity, WorkflowStatus } from 'types/Workflow';

export const showToast = (success: boolean, messages?: string[]) => {
    const options: ToastOptions<{}> = {
        position: 'top-right',
        toastId: Math.random(),
    };
    let MsgNode = null;
    if (messages && messages.length > 1) {
        MsgNode = (
            <div>
                {messages.map((message: string, index: number) => (
                    <p key={index}>{message}</p>
                ))}
            </div>
        );
    }
    if (success) {
        if (!isEmpty(messages)) {
            if (messages!.length === 1) {
                toast.success(messages![0], options);
            } else {
                toast.success(MsgNode, options);
            }
        }
    } else {
        if (!isEmpty(messages)) {
            if (messages!.length === 1) {
                toast.error(messages![0], options);
            } else {
                toast.error(MsgNode, options);
            }
        }
    }
};

export const genTableIndex = (index: number, limit: number, currentPage: number) =>
    index + limit * (currentPage - 1) + 1;

export const getFieldHtml = (fields: ItemParam[], id: number, t: TFunction<'translation', undefined>) => {
    const item = find(fields, { id }) as ItemParam;
    if (item) {
        return <span className={item.className}>{t(`constants.${item.name}`)}</span>;
    }
    return <></>;
};

export const getFieldInArrayObject = (
    listObj: {}[],
    id: number | string,
    fieldName: string = 'name',
    defaultValue: string = '',
    fieldCompare = 'id'
) => get(find(listObj, { [fieldCompare]: id }), fieldName, defaultValue);

export const toggleModalOpen = (show: boolean) => {
    if (show) {
        document.body.classList.add('modal-open');
    } else {
        document.body.classList.remove('modal-open');
    }
};

export const selectItem = (listItems: ItemParam[], t: TFunction<'translation', undefined>, noNoneOption?: boolean) => {
    const selectOptions: JSX.Element[] = [];
    if (!noNoneOption) {
        selectOptions.push(
            <option key={0} value={0}>
                --
            </option>
        );
    }
    listItems.forEach((item) => {
        selectOptions.push(
            <option key={item.id} value={item.id}>
                {t(`constants.${item.name}`)}
            </option>
        );
    });
    return selectOptions;
};

export const convertConstantToSelectOptions = (
    items: ItemParam[],
    t: TFunction<'translation', undefined>,
    noNoneOption?: boolean
) => {
    const selectOptions: SelectOption[] = [];
    if (!noNoneOption) {
        selectOptions.push({
            value: 0,
            label: '',
        });
    }
    items.forEach((item) => {
        selectOptions.push({
            value: item.id,
            label: t(`constants.${item.name}`),
        });
    });
    return selectOptions;
};

/**
 * Chuyển đổi danh sách các giá trị có type là string thành SelectOption
 * @param items Danh sách các item có id là string
 * @param t Hàm dịch
 * @param noNoneOption Không thêm option trống
 * @returns Danh sách SelectOptionString
 */
export const convertStringConstantToSelectOptions = (
    items: { id: string; name: string }[],
    t: TFunction<'translation', undefined>,
    noNoneOption?: boolean
) => {
    const selectOptions: SelectOptionString[] = [];
    if (!noNoneOption) {
        selectOptions.push({
            value: '',
            label: '',
        });
    }
    items.forEach((item) => {
        selectOptions.push({
            value: item.id,
            label: t(`constants.${item.name}`),
        });
    });
    return selectOptions;
};

// tslint:disable-next-line
export const getSelectStyle = (baseStyles: any, state: any, isError: boolean) => ({
    ...baseStyles,
    borderColor: state.isFocused ? (isError ? '#ea5455' : baseStyles.borderColor) : isError ? '#ea5455' : '#d8d6de',
    boxShadow: 'none',
});

export const isValidImageFile = (file: File) => {
    const validImageTypes = [
        'image/png',
        'image/jpg',
        'image/jpeg',
        'image/gif',
        'image/webp',
        'image/bmp',
        'image/svg+xml',
    ];
    return file && validImageTypes.includes(file.type);
};

/* tslint:disable:no-any */
export function flattenObject(obj: any, prefix = ''): Record<string, any> {
    return Object.keys(obj).reduce((acc, k) => {
        const pre = prefix.length ? prefix + '.' : '';
        if (typeof obj[k] === 'object' && obj[k] !== null && !Array.isArray(obj[k])) {
            Object.assign(acc, flattenObject(obj[k], pre + k));
        } else {
            acc[pre + k] = obj[k];
        }
        return acc;
    }, {} as Record<string, any>);
}

export const convertPaging = <T, K extends { page?: string; limit?: string }>(
    data: DataList<T>,
    limit: number
): Paging => ({
    count_item: data?.totalCount,
    total_page: data?.totalPages,
    current_page: data?.currentPage, // Number(paramConfig.page),
    limit,
});

export const generateFilters = (params: any, filterConfig: any) => {
    const filters = [];
    for (const paramKey of Object.keys(params)) {
        const value = params[paramKey];
        const config = filterConfig[paramKey];

        if (config && value) {
            const { key, operator } = config;
            filters.push(`${key}:${operator}(${value})`);
        }
    }
    return filters;
};

export function getStatusColor(status: string): string {
    switch (status.toLowerCase()) {
        case 'activated':
            return 'success';
        case 'draft':
            return 'warning';
        case 'inactivated':
            return 'secondary';
        default:
            return 'primary';
    }
}

export const handleGraphQLError = (error: Error): void => {
    if ('errors' in error && Array.isArray(error.errors)) {
        const listErrs: string[] = [];
        error.errors[0].errors.forEach((item: string) => {
            listErrs.push(item.split(':')[1]);
        });
        showToast(false, listErrs);
        return;
    } else {
        showToast(false, [COMMON_MESSAGE.ERROR_MESSAGE]);
        return;
    }
};

export const getStatusBadgeStep = (status: TaskStatus) => {
    switch (status) {
        case TaskStatus.IN_PROGRESS:
            return <span className="badge rounded-pill bg-light-warning">In Progress</span>;
        case TaskStatus.APPROVED:
            return <span className="badge rounded-pill bg-light-success">Approved</span>;
        case TaskStatus.TERMINATED:
            return <span className="badge rounded-pill bg-light-danger">Terminated</span>;
        case TaskStatus.REJECTED:
            return <span className="badge rounded-pill bg-light-danger">Rejected</span>;
        case TaskStatus.SUBMITTED:
            return <span className="badge rounded-pill bg-light-success">Submitted</span>;
        case TaskStatus.DELETED:
            return <span className="badge rounded-pill bg-light-danger">Deleted</span>;
        default:
            return <span className="badge rounded-pill bg-light-secondary"></span>;
    }
};

export const getStatusBadgeStepCircle = (status: TaskStatus) => {
    switch (status) {
        case TaskStatus.IN_PROGRESS:
            return <div className="rounded-pill bg-light-warning !tw-w-5 !tw-h-5"></div>;
        case TaskStatus.APPROVED:
            return <div className="rounded-pill bg-light-success !tw-w-5 !tw-h-5"></div>;
        case TaskStatus.TERMINATED:
            return <div className="rounded-pill bg-light-danger !tw-w-5 !tw-h-5"></div>;
        case TaskStatus.REJECTED:
            return <div className="rounded-pill bg-light-danger !tw-w-5 !tw-h-5"></div>;
        case TaskStatus.SUBMITTED:
            return <div className="rounded-pill bg-light-success !tw-w-5 !tw-h-5"></div>;
        case TaskStatus.DELETED:
            return <div className="rounded-pill bg-light-danger !tw-w-5 !tw-h-5"></div>;
        default:
            return <div className="rounded-pill bg-light-secondary !tw-w-5 !tw-h-5"></div>;
    }
};

export const getStatusTypeChangeRequest = (status: 'TEMPORARY' | 'PERMANENT') => {
    switch (status) {
        case 'TEMPORARY':
            return <span className="badge rounded-pill bg-light-success">Temporary</span>;
        case 'PERMANENT':
            return <span className="badge rounded-pill bg-light-warning">Permanent</span>;

        default:
            return <span className="badge rounded-pill bg-light-secondary"></span>;
    }
};

export const getStatusBadgeWorkflow = (status: string) => {
    switch (status) {
        case 'IN_PROGRESS':
            return <span className="badge rounded-pill bg-light-warning">In Progress</span>;
        case 'APPROVED':
            return <span className="badge rounded-pill bg-light-success">Approved</span>;
        case 'TERMINATED':
            return <span className="badge rounded-pill bg-light-danger">Terminated</span>;
        case 'REJECTED':
            return <span className="badge rounded-pill bg-light-danger">Rejected</span>;
        case 'DRAFT':
            return <span className="badge rounded-pill bg-light-secondary">Draft</span>;
        case 'COMPLETED':
            return <span className="badge rounded-pill bg-light-success">Completed</span>;

        default:
            return <span className="badge rounded-pill bg-light-secondary">{status}</span>;
    }
};

export const getStatusBadgeWorkflowList = (status: WorkflowStatus) => {
    switch (status) {
        case WorkflowStatus.DRAFT:
            return <span className="badge rounded-pill bg-light-warning">Draft</span>;
        case WorkflowStatus.ACTIVE:
            return <span className="badge rounded-pill bg-light-success">Active</span>;
        case WorkflowStatus.INACTIVE:
            return <span className="badge rounded-pill bg-light-danger">Inactive</span>;
        default:
            return <span className="badge rounded-pill bg-light-secondary">Inactive</span>;
    }
};

export const getTrackingStatusBadge = (status: string) => {
    switch (status) {
        case TrackingStatus.ON_TRACKING:
            return <span className="badge rounded-pill bg-light-warning tw-capitalize">on tracking</span>;
        case TrackingStatus.COMPLETED:
            return <span className="badge rounded-pill bg-light-success tw-capitalize">Completed</span>;
        case TrackingStatus.CANCELLED:
            return <span className="badge rounded-pill bg-light-danger tw-capitalize">Cancelled</span>;
        case TrackingStatus.OVERDUE:
            return <span className="badge rounded-pill bg-light-warning tw-capitalize">Overdue</span>;
        case TrackingStatus.BACKLOG:
            return <span className="badge rounded-pill bg-light-primary tw-capitalize">Backlog</span>;
        case TrackingStatus.DRAFT:
            return <span className="badge rounded-pill bg-light-secondary">Draft</span>;
        default:
            return <span className="badge rounded-pill bg-light-secondary tw-capitalize">{status}</span>;
    }
};

export function isDateValid(dateStr: Date | string) {
    return new Date(dateStr).toString() !== 'Invalid Date';
}

export function isValidFormApproval(data: UserTaskEntity[]): boolean {
    const requiredKeys = ['remark', 'Approve'];
    const allowedKeys = ['remark', 'Approve', 'Reject', 'Terminate', 'Button', 'Close'];

    const formFields = data?.[0]?.form?.schema?.form_fields;
    if (!Array.isArray(formFields)) return false;

    const hasAllRequired = requiredKeys.every((key) => formFields.some((field) => field.key === key));

    const allKeysValid = formFields.every((field) => allowedKeys.includes(field.key));

    return hasAllRequired && allKeysValid;
}

export const getOrdinalSuffix = (n: number): string => {
    const suffixes = ['th', 'st', 'nd', 'rd'];
    const v = n % 100;
    return `${n}${suffixes[(v - 20) % 10] || suffixes[v] || suffixes[0]}`;
};

export const parseDateRangeValue = (value: string): [Date, Date] | null => {
    if (!value) return null;

    const parts = value.split('-');
    if (parts.length !== 2) return null;

    try {
        const startDate = new Date(parts[0].trim());
        const endDate = new Date(parts[1].trim());

        if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
            return null;
        }

        return [startDate, endDate];
    } catch {
        return null;
    }
};

export function getFormTasksWithOrder(tasks: UserTaskEntity[], dataUserTaskDetail?: Task): UserTaskEntity[] {
    const groupedByFormId: Record<string, UserTaskEntity[]> = {};

    tasks?.forEach((item) => {
        const formId = item?.form_id;
        if (!formId) return;
        if (!groupedByFormId[formId]) {
            groupedByFormId[formId] = [];
        }
        groupedByFormId[formId].push(item);
    });

    const result: UserTaskEntity[] = [];

    Object.entries(groupedByFormId).forEach(([formId, taskGroup]) => {
        const tasksWithOrder = taskGroup.filter((t) => (t?.order ?? 0) === dataUserTaskDetail?.order);
        result.push(...tasksWithOrder);
    });

    return result;
}

export function getFormTasksWithOrderPreForm(tasks: UserTaskEntity[]): UserTaskEntity[] {
    const groupedByFormId: Record<string, UserTaskEntity[]> = {};

    tasks?.forEach((item) => {
        const formId = item?.form_id;
        if (!formId) return;
        if (!groupedByFormId[formId]) {
            groupedByFormId[formId] = [];
        }
        groupedByFormId[formId].push(item);
    });

    const result: UserTaskEntity[] = [];

    Object.values(groupedByFormId).forEach((taskGroup) => {
        const maxOrder = Math.max(...taskGroup.map((t) => t?.order ?? 0));
        const tasksWithMaxOrder = taskGroup.filter((t) => (t?.order ?? 0) === maxOrder);
        result.push(...tasksWithMaxOrder);
    });

    return result;
}
